package csim.model.api;

/**
 * 模型处理器
 */
public abstract class ModelObjectHandler<T extends ModelObject> {

    protected  ModelContext<T> context;

    public final void setContext(ModelContext<T> context) {
        this.context = context;
    }


    public void onJoinSimulation(long logicTime){}
    /**
     * 调度
     */
    public abstract void update(long delta);

    /**
     * 外部消息
     * @param message
     */
    public abstract void onMessage(Object message);
}
