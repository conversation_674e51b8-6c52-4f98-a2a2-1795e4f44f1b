package csim.model.api;

import csim.model.api.annotation.AtomicModelInvoke;
import csim.model.api.annotation.GlobalInvoke;
import csim.toolkit.support.SFunction;

import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 对外上下文
 */
public interface ModelContext<T extends ModelObject> {

    /**
     * 获取模型对象
     * @return
     */
    T getModelObject();


    /**
     * 获取父级上下文
     * 一般用于 组件内获取平台
     *
     * @return
     * @param <P>
     */
    <P extends ModelObject> ModelContext<P> getParentModelContext();

    /**
     * 订阅属性
     * @param attrGetter
     * @param <SUBT>
     * @param <SUBATTR>
     */
    <SUBT, SUBATTR>void subAttribute(SFunction<SUBT,SUBATTR> attrGetter);
}
