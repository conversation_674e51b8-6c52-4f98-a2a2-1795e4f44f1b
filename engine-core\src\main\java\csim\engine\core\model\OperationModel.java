package csim.engine.core.model;

import csim.engine.core.event.SetAttributeMsg;
import csim.engine.core.event.TickMsg;
import csim.model.api.ModelContext;
import csim.model.api.ModelObject;
import csim.model.api.ModelObjectHandler;
import csim.model.api.annotation.ThisInvoke;
import csim.toolkit.support.SFunction;

import javax.annotation.Nullable;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

public class OperationModel<T extends ModelObject> extends AtomicModel implements ModelContext {
    /**
     * 业务模型处理器
     */
    private final ModelObjectHandler<T> handler;

    private final T modelObject;

    protected OperationModel(@Nullable AtomicModel parent, String id, long simStartTime,
                             ModelObjectHandler handler, T modelObject) {
        this(parent, null, id, simStartTime, handler, modelObject);
    }

    protected OperationModel(@Nullable AtomicModel parent, @Nullable Actor executorParent, String id, long simStartTime,
                             ModelObjectHandler handler, T modelObject) {
        super(parent,executorParent, id, simStartTime);
        this.handler = handler;
        this.modelObject = modelObject;
        this.handler.setContext(this);
    }


    @Override
    protected void update(long delta) {
        if (handler != null) {
            handler.update(delta);
        }
    }

    @Override
    public T getModelObject() {
        return modelObject;
    }

    @Override
    public void subAttribute(SFunction attrGetter) {

    }

    @Override
    public  ModelContext getParentModelContext() {
        if (this.parent instanceof ModelContext parentModelContext) {
            return parentModelContext;
        }
        return null;
    }

    @Override
    protected void onJoinSimulation(long logicTime) {
        handler.onJoinSimulation(logicTime);
    }
}
