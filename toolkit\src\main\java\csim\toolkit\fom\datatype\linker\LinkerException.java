/*
 *   Copyright 2017 The Portico Project
 *
 *   This file is part of portico.
 *
 *   portico is free software; you can redistribute it and/or modify
 *   it under the terms of the Common Developer and Distribution License (CDDL) 
 *   as published by Sun Microsystems. For more information see the LICENSE file.
 *   
 *   Use of this software is strictly AT YOUR OWN RISK!!!
 *   If something bad happens you do not have permission to come crying to me.
 *   (that goes for your lawyer as well)
 *
 */
package csim.toolkit.fom.datatype.linker;

/**
 * Indicates that the {@link Linker} encountered an error while attempting to resolve a
 * {@link DatatypePlaceholder} to its actual representation.
 */
public class LinkerException extends Exception
{
	//----------------------------------------------------------
	//                    STATIC VARIABLES
	//----------------------------------------------------------

	//----------------------------------------------------------
	//                   INSTANCE VARIABLES
	//----------------------------------------------------------

	//----------------------------------------------------------
	//                      CONSTRUCTORS
	//----------------------------------------------------------

	//----------------------------------------------------------
	//                    INSTANCE METHODS
	//----------------------------------------------------------
	/**
	 * Just create an empty exception
	 */
	public LinkerException()
	{
		super();
	}

	/**
	 * @param message The message to create the exception with
	 */
	public LinkerException( String message )
	{
		super( message );
	}

	/**
	 * @param cause The cause of the exception
	 */
	public LinkerException( Throwable cause )
	{
		super( cause );
	}

	/**
	 * @param message The message to create the exception with
	 * @param cause The cause of the exception
	 */
	public LinkerException( String message, Throwable cause )
	{
		super( message, cause );
	}

	//----------------------------------------------------------
	//                     STATIC METHODS
	//----------------------------------------------------------
}
