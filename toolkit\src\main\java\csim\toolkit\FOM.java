/*
 *   Copyright 2012 The Portico Project
 *
 *   This file is part of portico.
 *
 *   portico is free software; you can redistribute it and/or modify
 *   it under the terms of the Common Developer and Distribution License (CDDL)
 *   as published by Sun Microsystems. For more information see the LICENSE file.
 *
 *   Use of this software is strictly AT YOUR OWN RISK!!!
 *   If something bad happens you do not have permission to come crying to me.
 *   (that goes for your lawyer as well)
 *
 */
package csim.toolkit;

import csim.toolkit.fom.exception.JConfigurationException;
import csim.toolkit.fom.exception.JCouldNotOpenFED;
import csim.toolkit.fom.exception.JErrorReadingFED;
import csim.toolkit.fom.*;
import csim.toolkit.fom.datatype.EnumeratedType;
import csim.toolkit.fom.datatype.IDatatype;
import csim.toolkit.fom.datatype.SimpleType;
import csim.toolkit.fom.datatype.linker.DatatypePlaceholder;
import csim.toolkit.fom.FedHelpers;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * The FOM paser for 1516e style federation description documents.
 */
public class FOM {
    //----------------------------------------------------------
    //                    STATIC VARIABLES
    //----------------------------------------------------------
    private static final ParserWrapper PARSER = new ParserWrapper();

    //----------------------------------------------------------
    //                   INSTANCE VARIABLES
    //----------------------------------------------------------
    private ObjectModel fom;

    //----------------------------------------------------------
    //                      CONSTRUCTORS
    //----------------------------------------------------------
    private FOM() {
        this.fom = new ObjectModel(HLAVersion.IEEE1516e);
    }

    //----------------------------------------------------------
    //                    INSTANCE METHODS
    //----------------------------------------------------------

    /**
     * The main FOM parser processing method. This takes the root element of a FOM document and
     * parses it into an {@link ObjectModel} that is returned. If the parser is malformatted in
     * any way, a {@link JConfigurationException} is thrown.
     * <p/>
     * <b>Note:</b> Datatypes, Attributes and Parameters in the returned FOM will contain
     * placeholder symbols as the datatypes that they refer to may be declared in another FOM
     * module. Once all modules have been merged into a combined FOM, call
     * {@link ObjectModel#resolveSymbols(ObjectModel)} to resolve all placeholder datatypes to their
     * concrete representation
     */
    public ObjectModel process(Element element, URL fed) throws JErrorReadingFED {
        // locate the major elements we are interested in
        Element datatypesElement = null;
        Element objectsElement = null;
        Element interactionsElement = null;
        Element dimensionsElement = null;
        Element modelIdentificationElement = null;
        for (Element temp : FedHelpers.getChildElements(element)) {
            String tagName = temp.getTagName();
            if (tagName.equals("dataTypes"))
                datatypesElement = temp;
            else if (tagName.equals("objects"))
                objectsElement = temp;
            else if (tagName.equals("interactions"))
                interactionsElement = temp;
            else if (tagName.equals("dimensions"))
                dimensionsElement = temp;
            else if (tagName.equals("modelIdentification"))
                modelIdentificationElement = temp;
            else
                continue; // ignore
        }

        Element nameElement = (Element) modelIdentificationElement.getElementsByTagName("name").item(0);
        if (nameElement != null) {
            this.fom.setModuleName(nameElement.getFirstChild().getTextContent());
        } else {
            this.fom.setModuleName("all");
        }

        Element versionElement = (Element) modelIdentificationElement.getElementsByTagName("version").item(0);
        if (versionElement != null) {
            this.fom.setModuleVersion(versionElement.getFirstChild().getTextContent());
        } else {
            this.fom.setModuleVersion("1.0");
        }
        NodeList references = modelIdentificationElement.getElementsByTagName("reference");
        if (references != null) {
            List<ModuleInfo> referenceFoms = new ArrayList<>();
            for (int i = 0; i < references.getLength(); i++) {
                Element reference = (Element) references.item(i);
                Element typeElement = (Element) reference.getElementsByTagName("type").item(0);
                if (typeElement.getFirstChild().getTextContent().equals("Dependency")) {
                    Element identificationElement = (Element) reference.getElementsByTagName("identification").item(0);
                    String info = identificationElement.getFirstChild().getTextContent();
                    String moduleName;
                    String moduleVersion;
                    if (info.contains(":")) {
                        String[] tmp = info.split(":");
                        moduleName = tmp[0];
                        moduleVersion = tmp[1];
                    } else {
                        moduleName = info;
                        moduleVersion = "1.0";
                    }
                    ModuleInfo moduleInfo = new ModuleInfo(moduleName, moduleVersion);
                    referenceFoms.add(moduleInfo);
                }
            }
            this.fom.getReferenceModules().addAll(referenceFoms);
        }

        // extract all datatypes (this must be done first so that we can reference the types when
        // we extract objects and interactions)
        if (datatypesElement != null)
            this.extractDatatypes(this.fom.getModuleName(), datatypesElement);

        // extract all the object classes
        OCMetadata objectRoot = null;
        if (objectsElement != null) {
            objectRoot = this.extractObjects(objectsElement);
        } else {
            objectRoot = this.fom.newObject("HLAobjectRoot", "");
            this.fom.addObjectClass(objectRoot);
        }
        this.fom.setObjectRoot(objectRoot);

        // extract all the interaction classes
        ICMetadata interactionRoot;
        if (interactionsElement != null) {
            interactionRoot = this.extractInteractions(interactionsElement);
        } else {
            interactionRoot = this.fom.newInteraction("HLAinteractionRoot","");
            this.fom.addInteractionClass(interactionRoot);
        }
        this.fom.setInteractionRoot(interactionRoot);

        if (dimensionsElement != null) {
            extractNormalizationServices(dimensionsElement);
        }

        this.fom.setFileName(fed.toString());
        // return the completed FOM
        return this.fom;
    }

    ////////////////////////////////////////////////////////////////////////////////////////////
    ///////////////////////////////////// Datatype Methods /////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////
    private void extractDatatypes(String moduleName, Element datatypesElement) throws JErrorReadingFED {
        Set<IDatatype> fedTypes = null;
        try {
            fedTypes = FedHelpers.extractDatatypes(moduleName, datatypesElement,
                    fom.getHlaVersion());
        } catch (JConfigurationException jce) {
            // rethrow as JErrorReadingFED
            throw new JErrorReadingFED(jce);
        }

        // Add types
        for (IDatatype fedType : fedTypes) {
            // We used to link datatypes here on the assumption that FOM modules were
            // self-contained. However it appears that is not the reality, and that modules can
            // reference datatypes that are only declared in other modules.
            //
            // As such we datatypes regardless of whether they contain placeholder symbols, and
            // resolve them once all modules have been merged
            fom.addDatatype(fedType);
        }
    }

    ////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////// Object Class Methods ///////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////

    /**
     * Loop through all the child objects and generate the metadata hierarchy to represent them.
     * <p/>
     * The general format of the object element is as follows:
     * <pre>
     * <objects>
     *    <objectClass>
     *       <name>HLAobjectRoot</name>
     *       <attribute>
     *          <name>name</name>
     *          <transportation>HLAreliable|HLAbestEffort</transportation>
     *          <order>TimeStamp|Receive</order>
     *       </attribute>
     *       ...
     *       <objectClass>
     *          <name>HLAmanager</name>
     *          <attribute/>...
     *       </objectClass>
     *    </objectClass>
     * </objects>
     * </pre>
     */
    private OCMetadata extractObjects(Element objectsElement) throws JErrorReadingFED {
        Element objectRootElement = FedHelpers.getFirstChildElement(objectsElement,
                "objectClass");
        if (objectRootElement == null) {
            // no objects to process... OK, must be an extension module
            return null;
        }

        // validate that we have an object root
        String name = FedHelpers.getChildValue(objectRootElement, "name");
        if (name.equals("HLAobjectRoot") == false) {
            throw new JErrorReadingFED("First <objectClass> must be HLAobjectRoot, found: " + name);
        }

        OCMetadata objectRoot = fom.newObject("HLAobjectRoot", "");
        extractAttributes(objectRoot, objectRootElement);
        fom.addObjectClass(objectRoot);

        // recurse and find all our children
        extractObjects(objectRoot, objectRootElement);
        return objectRoot;
    }

    private void extractObjects(OCMetadata parent, Element parentElement) throws JErrorReadingFED {
        List<Element> children = FedHelpers.getAllChildElements(parentElement, "objectClass");
        for (Element current : children) {
            String objectClassName = FedHelpers.getChildValue(current, "name");
            String semantics = "";
            try {
                semantics = FedHelpers.getChildValue(current, "semantics");
            }catch (JConfigurationException ex){

            }
            OCMetadata objectClass = fom.newObject(objectClassName,semantics);

            // get the sharing policy
            String objectClassSharing = FedHelpers.getChildValueForgiving(current,
                    "sharing", objectClassName);
            if (objectClassSharing != null)
                objectClass.setSharing(Sharing.fromFomString(objectClassSharing));

            // link us to our parent
            objectClass.setParent(parent);
            extractAttributes(objectClass, current);

            fom.addObjectClass(objectClass);
            // recurse and find all our children
            extractObjects(objectClass, current);
        }
    }

    /**
     * This method will extract all the relevant object class attributes (not XML attributes)
     * from the given "objectClass" element. For each attribute, an {@link ACMetadata} will be
     * created and stored inside the provided {@link OCMetadata}.
     */
    private void extractAttributes(OCMetadata clazz, Element element) throws JErrorReadingFED {
        ObjectModel theModel = clazz.getModel();
        List<Element> attributes = FedHelpers.getAllChildElements(element, "attribute");
        for (Element attributeElement : attributes) {
            String attributeName = FedHelpers.getChildValue(attributeElement, "name");

            // All attribute datatypes are initially created as placeholders, and resolved once
            // all FOM modules have been merged and the standard MIM has been inserted
            String datatypeName = FedHelpers.getChildValue(attributeElement, "dataType");
            IDatatype datatype = new DatatypePlaceholder(theModel.getModuleName(), datatypeName);

            String updateType = FedHelpers.getChildValue(attributeElement, "updateType");
            if (updateType == null) {
                updateType = "NA";
            }
            updateType = updateType.toUpperCase();

            String semantics = "";
            try {
                semantics = FedHelpers.getChildValue(attributeElement, "semantics");
            }catch (JConfigurationException ex){

            }
            ACMetadata attribute = fom.newAttribute(attributeName, updateType, datatype, semantics);

            // Order and Transport
            String attributeOrder = FedHelpers.getChildValueForgiving(attributeElement,
                    "order",
                    attributeName);
            if (attributeOrder != null)
                attribute.setOrder(Order.fromFomString(attributeOrder));

            String attributeTransport = FedHelpers.getChildValueForgiving(attributeElement,
                    "transportation",
                    attributeName);
            if (attributeTransport != null)
                attribute.setTransport(Transport.fromFomString(attributeTransport));

            // get the sharing policy
            String attributeSharing = FedHelpers.getChildValueForgiving(attributeElement,
                    "sharing",
                    attributeName);
            if (attributeSharing != null)
                attribute.setSharing(Sharing.fromFomString(attributeSharing));

            Element dimensionNode = FedHelpers.getFirstChildElement(attributeElement, "dimensions");
            if (dimensionNode != null) {
                List<Element> dimensionElements = FedHelpers.getAllChildElements(dimensionNode, "dimension");

                Space space = theModel.newSpace("space-" + attributeName);
                for (Element dimensionElement : dimensionElements) {
                    String dimensionName = dimensionElement.getTextContent();
                    Dimension dimension = theModel.newDimension(dimensionName);
                    space.addDimension(dimension);
                }
                attribute.setSpace(space);
            }

            // add the attribute to the containing class
            clazz.addAttribute(attribute);
        }
    }

    ////////////////////////////////////////////////////////////////////////////////////////////
    ///////////////////////////////// Interaction Class Methods ////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////

    /**
     * Loop through all the child objects and generate the metadata hierarchy to represent them.
     * <p/>
     * The general format of the object element is as follows:
     * <pre>
     * <interactions>
     *    <interactionClass>
     *       <name>HLAinteractionRoot</name>
     *       <transportation>HLAreliable|HLAbestEffort</transportation>
     *       <order>TimeStamp|Receive</order>
     *       <parameter>
     *          <name>name</name>
     *       </parameter>
     *       ...
     *       <interactionClass/>...
     *    </interactionClass>
     * </interactions>
     * </pre>
     */
    private ICMetadata extractInteractions(Element element) throws JErrorReadingFED {
        Element interactionRootElement = FedHelpers.getFirstChildElement(element,
                "interactionClass");
        if (interactionRootElement == null) {
            // no interactions to process... OK, must be an extension module
            return null;
        }

        // validate that we have an interaction root
        String name = FedHelpers.getChildValue(interactionRootElement, "name");
        if (name.equals("HLAinteractionRoot") == false) {
            throw new JErrorReadingFED("First <interactionClass> must be " +
                    "HLAinteractionRoot, found: " + name);
        }

        // generate some basic information for the Interaction root
        ICMetadata interactionRoot = fom.newInteraction("HLAinteractionRoot","");

        // get the transport and order
        String interactionOrder = FedHelpers.getChildValueForgiving(interactionRootElement, "order", name);
        if (interactionOrder != null)
            interactionRoot.setOrder(Order.fromFomString(interactionOrder));

        String interactionTransport = FedHelpers.getChildValueForgiving(interactionRootElement,
                "transportation",
                name);
        if (interactionTransport != null)
            interactionRoot.setTransport(Transport.fromFomString(interactionTransport));

        // get the sharing policy
        String interactionSharing = FedHelpers.getChildValueForgiving(interactionRootElement, "sharing", name);
        if (interactionSharing != null)
            interactionRoot.setSharing(Sharing.fromFomString(interactionSharing));

        // get the parameters
        extractParameters(interactionRoot, interactionRootElement);
        fom.addInteractionClass(interactionRoot);

        // recurse and find all our children
        extractInteractions(interactionRoot, interactionRootElement);
        return interactionRoot;
    }

    private void extractInteractions(ICMetadata parent, Element parentElement)
            throws JErrorReadingFED {
        List<Element> children = FedHelpers.getAllChildElements(parentElement,
                "interactionClass");
        for (Element current : children) {
            // create the metadata type
            String interactionClassName = FedHelpers.getChildValue(current, "name");
            String semantics = "";
            try {
                semantics = FedHelpers.getChildValue(current, "semantics");
            }catch (JConfigurationException ex){

            }
            ICMetadata interactionClass = fom.newInteraction(interactionClassName,semantics);

            // get the transport and order
            String interactionOrder = FedHelpers.getChildValueForgiving(current,
                    "order",
                    interactionClassName);
            if (interactionOrder != null)
                interactionClass.setOrder(Order.fromFomString(interactionOrder));

            String interactionTransport = FedHelpers.getChildValueForgiving(current,
                    "transportation",
                    interactionClassName);
            if (interactionTransport != null)
                interactionClass.setTransport(Transport.fromFomString(interactionTransport));


            String sharing = FedHelpers.getChildValueForgiving(current,
                    "sharing",
                    interactionClassName);
            if (sharing != null)
                interactionClass.setSharing(Sharing.fromFomString(sharing));
            // get all the interaction parameters
            extractParameters(interactionClass, current);

            // link us to our parent
            interactionClass.setParent(parent);
            fom.addInteractionClass(interactionClass);

            // recurse and find all our children
            extractInteractions(interactionClass, current);
        }
    }

    /**
     * This method will extract all the relevant interaction class parameters from the given
     * "interactionClass" element. For each parameter, a {@link PCMetadata} will be created
     * and stored inside the provided {@link ICMetadata}.
     */
    private void extractParameters(ICMetadata clazz, Element element) throws JErrorReadingFED {
        ObjectModel theModel = clazz.getModel();
        List<Element> parameters = FedHelpers.getAllChildElements(element, "parameter");
        for (Element parameterElement : parameters) {
            String parameterName = FedHelpers.getChildValue(parameterElement, "name");

            // All parameter datatypes are initially created as placeholders, and resolved once
            // all FOM modules have been merged and the standard MIM has been inserted
            String datatypeName = FedHelpers.getChildValue(parameterElement, "dataType");
            IDatatype datatype = new DatatypePlaceholder(theModel.getModuleName(), datatypeName);


            String semantics = "";
            try {
                semantics = FedHelpers.getChildValue(parameterElement, "semantics");
            }
            catch (JConfigurationException ex){

            }
            PCMetadata parameter = fom.newParameter(parameterName, datatype, semantics);
            clazz.addParameter(parameter);
        }
    }

    private void extractNormalizationServices(Element element) {
        List<Element> dimensions = FedHelpers.getAllChildElements(element, "dimension");
        for (Element dimension : dimensions) {
            String name = FedHelpers.getChildValue(dimension, "name");
            NormalizationServiceMetadata metadata = fom.newNormalizationServiceMetadata(name);

            String dataTypeName = FedHelpers.getFirstChildElement(dimension, "dataType").getTextContent();

            IDatatype datatype = fom.getDatatype(dataTypeName);
            if (!(datatype instanceof SimpleType) && !(datatype instanceof EnumeratedType)) {
                throw new JConfigurationException("normalization service " + name + " should return a SimpleType or EnumeratedType which representation should be a" +
                        "HLAinteger32LE or HLAinteger32BE!");
            }
            String representation = null;
            if (datatype instanceof SimpleType) {
                representation = ((SimpleType) datatype).getRepresentation().getName();
            }
            else {
                representation = ((EnumeratedType) datatype).getRepresentation().getName();
            }
            if (!representation.equals("HLAinteger32LE")&& !representation.equals("HLAinteger32BE")) {
                throw new JConfigurationException("normalization service " + name + " should return a simpleDatatype which representation should be a" +
                        "HLAinteger32LE!");
            }
            metadata.setDatatype(datatype);

            String normalization = FedHelpers.getFirstChildElement(dimension, "normalization").getTextContent();
            metadata.setNormalization(normalization);

            Element upperBoundElement = FedHelpers.getFirstChildElement(dimension, "upperBound");
            if (upperBoundElement == null || upperBoundElement.getTextContent().isEmpty()) {
                metadata.setUpperBound(Integer.MAX_VALUE);
            } else {
                metadata.setUpperBound(Integer.parseInt(upperBoundElement.getTextContent()));
            }

            String value = FedHelpers.getFirstChildElement(dimension, "value").getTextContent();
            NormalizationValueEnum normalizationValueEnum = NormalizationValueEnum.fromFomString(value);
            metadata.setValue(normalizationValueEnum);

            fom.addNormalizationService(metadata);
        }
    }

    ////////////////////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////// Private Helper Methods //////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////////////////////
    //----------------------------------------------------------
    //                     STATIC METHODS
    //----------------------------------------------------------

    /**
     * This method will take the given 1516e XML FOM and attempt to process it.
     * If there is a problem locating the URL or fetching the stream with which to read it, a
     * {@link JCouldNotOpenFED} exception will be thrown. If there is a problem reading the FOM,
     * or its structure is not correct, an {@link JErrorReadingFED} exception will be thrown.
     * <p/>
     * If the FOM is correct and can be processed successfully, it will be turned into an
     * {@link ObjectModel} and returned.
     *
     * @param fed The URL location of the fed file (could be local or remote)
     * @return An {@link ObjectModel} representing the FOM from the given location
     * @throws JCouldNotOpenFED If the fed file can not be located or there is an error opening it
     * @throws JErrorReadingFED If the fed file is invalid or there is a problem reading it from the
     *                          stream.
     */
    public static ObjectModel parseFOM(URL fed) throws JCouldNotOpenFED, JErrorReadingFED {
        ////////////////////////////////////////
        // parse the fed file into a DOM-tree //
        ////////////////////////////////////////
        if (fed == null)
            throw new JCouldNotOpenFED("Can't locate fed file: " + fed);

        // try to open a stream to the given URL
        Element rootElement = null;
        try {
            // open the file
            InputStream stream = fed.openStream();
            // parse the thing in
            Document document = PARSER.parse(stream);
            document.normalize();
            rootElement = document.getDocumentElement();
            // close off the stream
            stream.close();
        } catch (IOException ioex) {
            throw new JCouldNotOpenFED("Error opening fed file: " + ioex.getMessage(), ioex);
        } catch (Exception e) {
            throw new JErrorReadingFED("Error reading fed file: " + e.getMessage(), e);
        }

        /////////////////////
        // process the FOM //
        /////////////////////
        try {
            // create the parser
            FOM parser = new FOM();
            // we'll call the node handler directly because we want to process the root node
            ObjectModel model = parser.process(rootElement, fed);
            return model;
        } catch (NullPointerException npe) {
            throw new JErrorReadingFED("Problem while reading fed file", npe);
        }
    }
}
