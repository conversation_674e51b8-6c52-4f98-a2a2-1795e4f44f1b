package csim.maven.plugin.processor;

import com.sun.source.tree.MethodTree;
import com.sun.source.tree.Tree;
import com.sun.source.util.TreeScanner;
import com.sun.source.util.Trees;
import com.sun.tools.javac.code.TypeTag;
import com.sun.tools.javac.tree.JCTree;
import com.sun.tools.javac.tree.TreeMaker;
import com.sun.tools.javac.util.List;
import com.sun.tools.javac.util.Name;
import com.sun.tools.javac.util.Names;

import static com.sun.tools.javac.code.Flags.PARAMETER;

public class AsyncModelTreeScanner extends TreeScanner<Void, Void> {

    static final String var_name_prefix = "__csim__";


    static final String executor_var_name = var_name_prefix + "executor";
    static final String future_var_name = var_name_prefix + "future";
    static final String exception_var_name = var_name_prefix + "ex";

    private Trees trees;
    private TreeMaker treeMaker;
    private Names names;

    public AsyncModelTreeScanner(Trees trees, TreeMaker treeMaker, Names names) {
        this.trees = trees;
        this.treeMaker = treeMaker;
        this.names = names;
    }

    @Override
    public Void visitMethod(MethodTree node, Void aVoid) {

        //根据上下文设置位置
        Void unused = super.visitMethod(node, aVoid);
        String name = node.getName().toString();
        if (name.equals("<init>") || isStaticMethod((JCTree.JCMethodDecl) node) || hasSyncMethodAnnotation((JCTree.JCMethodDecl) node)) {
            return unused;
        }

        ((JCTree.JCMethodDecl) node).body = createNewBody(node);
        return super.visitMethod(node, aVoid);
    }

    private JCTree.JCBlock createNewBody(MethodTree node) {

        JCTree.JCMethodDecl methodDecl = (JCTree.JCMethodDecl) node;

        treeMaker.at(methodDecl.body.pos);


        // 2. 构建条件: executor != null && !executor.inEventLoop()
        JCTree.JCExpression condition = treeMaker.Binary(
                JCTree.Tag.NE,
                treeMaker.Select(treeMaker.Ident(names.fromString("this")), names.fromString("executor")),
                treeMaker.Literal(TypeTag.BOT, null)
        );


        condition = treeMaker.Binary(
                JCTree.Tag.AND,
                condition,
                treeMaker.Unary(
                        JCTree.Tag.NOT,
                        treeMaker.Apply(
                                List.nil(),
                                treeMaker.Select(treeMaker.Select(treeMaker.Ident(names.fromString("this")), names.fromString("executor")), names.fromString("inEventLoop")),
                                List.nil()
                        )
                )
        );


        if (isVoidReturn(methodDecl)) {
            JCTree.JCIf ifStmt = treeMaker.If(condition,
                    createVoidExecuteBody(methodDecl),
                    //原始方法体
                    treeMaker.Block(0, (methodDecl).body.getStatements()));

            List<JCTree.JCStatement> list = List.of( ifStmt);
            return treeMaker.Block(0, list);
        } else {

            JCTree.JCIf ifStmt = treeMaker.If(condition,
                    createNoneVoidSubmitBody(methodDecl),
                    //原始方法体
                    treeMaker.Block(0, (methodDecl).body.getStatements()));


            List<JCTree.JCStatement> list = List.of(ifStmt);

            return treeMaker.Block(0, list);
        }
    }


    /**
     * 返回值不是void的方法体
     *
     * @param methodDecl
     * @return
     */
    private JCTree.JCStatement createNoneVoidSubmitBody(JCTree.JCMethodDecl methodDecl) {
        List<JCTree.JCExpression> args = List.nil();
        for (JCTree.JCVariableDecl param : methodDecl.params) {
            args = args.append(treeMaker.Ident(param.name));
        }




        JCTree.JCLambda lambda = treeMaker.Lambda(List.nil(), treeMaker.Block(0, List.of(treeMaker.Return(
                treeMaker.Apply(
                        List.nil(),
                        treeMaker.Select(treeMaker.Ident(names.fromString("this")), methodDecl.name),
                        args // 调用 this.tick(logicTime)
                )
        ))));



        // 3.2 构造 executor.submit(() -> this.next())
        JCTree.JCExpression submitCall = treeMaker.Apply(
                List.nil(),
                treeMaker.Select(
                        treeMaker.Select(treeMaker.Ident(names.fromString("this")), names.fromString("executor")),
                        names.fromString("submit")
                ),
                List.of(lambda)
        );


        // 3.5 定义 future 变量
        JCTree.JCVariableDecl futureVar = treeMaker.VarDef(
                treeMaker.Modifiers(0),
                names.fromString(future_var_name),
                chainDots("io", "netty","util", "concurrent", "Future"),
                submitCall
        );


        JCTree.JCMethodInvocation futureGet = treeMaker.Apply(
                List.nil(),
                treeMaker.Select(treeMaker.Ident(names.fromString(future_var_name)), names.fromString("get")),
                List.nil()
        );


        JCTree.JCTypeCast cast = treeMaker.TypeCast(
                methodDecl.restype,
                futureGet
        );


        // 4.3 try 块：直接 return newCSimFuture.get()
        JCTree.JCBlock tryBlock = treeMaker.Block(0, List.of(treeMaker.Return(cast)));


        Name exceptionName = names.fromString(exception_var_name);
        // 4.4 构造 catch 块
        JCTree.JCVariableDecl exceptionParam = treeMaker.VarDef(
                treeMaker.Modifiers(PARAMETER),
                names.fromString(exception_var_name),
                treeMaker.Ident(names.fromString("Exception")),
                null
        );

        // throw new RuntimeException(e);
        JCTree.JCThrow throwRuntimeEx = treeMaker.Throw(
                treeMaker.NewClass(
                        null,
                        List.nil(),
                        treeMaker.Ident(names.fromString("RuntimeException")),
                        List.of(treeMaker.Ident(names.fromString(exception_var_name))),
                        null
                )
        );

        JCTree.JCCatch catchBlock = treeMaker.Catch(
                exceptionParam,
                treeMaker.Block(0, List.of(throwRuntimeEx))
        );


        // 4.5 最终 try-catch 结构
        JCTree.JCTry tryCatch = treeMaker.Try(tryBlock, List.of(catchBlock), null);
        tryCatch.pos = methodDecl.pos;
        List<JCTree.JCStatement> trueBranchStmts = List.of(futureVar, tryCatch);

        return treeMaker.Block(0, trueBranchStmts);
    }

    /**
     * 返回值为 void 的方法体
     *
     * @param methodDecl
     * @return
     */
    private JCTree.JCStatement createVoidExecuteBody(JCTree.JCMethodDecl methodDecl) {
        List<JCTree.JCExpression> args = List.nil();
        for (JCTree.JCVariableDecl param : methodDecl.params) {
            args = args.append(treeMaker.Ident(param.name));
        }


        JCTree.JCLambda lambda = treeMaker.Lambda(
                List.nil(), // Lambda 参数列表（无参数）
                treeMaker.Block(0, List.of(
                        treeMaker.Exec(
                                treeMaker.Apply(
                                        List.nil(),
                                        treeMaker.Select(treeMaker.Ident(names.fromString("this")), methodDecl.name),
                                        args // 调用 this.tick(logicTime)
                                )
                        )
                ))
        );

        JCTree.JCExpression executeCall = treeMaker.Apply(
                List.nil(),
                treeMaker.Select(treeMaker.Select(treeMaker.Ident(names.fromString("this")), names.fromString("executor")), names.fromString("execute")),
                List.of(lambda)
        );
        return treeMaker.Exec(executeCall);
    }


    public static boolean isVoidReturn(JCTree.JCMethodDecl methodDecl) {
        Tree.Kind kind = methodDecl.restype.getKind();
        if (kind == Tree.Kind.PRIMITIVE_TYPE) {
            return ((JCTree.JCPrimitiveTypeTree) methodDecl.restype).typetag == TypeTag.VOID;
        } else {
            return false;
        }
    }

    public static boolean isStaticMethod(JCTree.JCMethodDecl methodDecl) {
        // 获取方法的修饰符
        JCTree.JCModifiers modifiers = methodDecl.mods;

        // 检查修饰符中是否包含 static 标志
        return (modifiers.flags & com.sun.tools.javac.code.Flags.STATIC) != 0;
    }

    public static boolean hasSyncMethodAnnotation(JCTree.JCMethodDecl methodDecl) {
        List<JCTree.JCAnnotation> annotations = methodDecl.mods.annotations;

        for (JCTree.JCAnnotation annotation : annotations) {
            if (annotation.annotationType.toString().equals("SyncMethod")) {
                return true;
            }
        }
        return false;
    }

    private JCTree.JCExpression chainDots(String... elems) {
        JCTree.JCExpression expr = treeMaker.Ident(names.fromString(elems[0]));
        for (int i = 1; i < elems.length; i++) {
            expr = treeMaker.Select(expr, names.fromString(elems[i]));
        }
        return expr;
    }

    private JCTree.JCExpression chainDots(String elems) {
        String[] split = elems.split("\\.");
        return chainDots(split);
    }

}

