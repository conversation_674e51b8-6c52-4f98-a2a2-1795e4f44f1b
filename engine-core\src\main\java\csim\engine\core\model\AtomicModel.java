package csim.engine.core.model;

import csim.engine.core.event.AddChildMsg;
import csim.engine.core.event.OnJoinMsg;
import csim.engine.core.event.TickCompleteMsg;
import csim.engine.core.event.TickMsg;
import csim.model.api.annotation.ThisInvoke;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import javax.annotation.Nullable;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/**
 * 引擎模型调度层面
 */
@Slf4j
public class AtomicModel extends Actor<Object> {

    /**
     * 尚未被调度的对象
     */
    protected final Set<AtomicModel> unScheduled = new HashSet<>();
    protected final long simStartTime;
    protected final AtomicModel parent;
    protected final Set<AtomicModel> children = new HashSet<>();
    protected int childrenTickCount = 0;
    private TreeMap<Long, Set<AtomicModel>> scheduleEvent = new TreeMap<>();
    /**
     * 上次调度的时间
     */
    private long lastTickTime;
    /**
     * 当前时间
     */
    private long currentTime;

    protected AtomicModel(
            @Nullable AtomicModel parent,
            String id,
            long simStartTime) {
        this(parent, null, id, simStartTime);
    }


    protected AtomicModel(
            @Nullable AtomicModel parent,
            @Nullable Actor executorParent,
            String id,
            long simStartTime) {
        super(executorParent);
        Assert.hasText(id, "id must not be null");
        this.parent = parent;
        this.simStartTime = simStartTime;
        this.currentTime = simStartTime;
        this.lastTickTime = currentTime;
    }


    private void doAddChild(AtomicModel child) {
        unScheduled.add(child);
        this.children.add(child);
        child.send(new OnJoinMsg(getCurrentTime()));
    }

    protected void onJoinSimulation(long logicTime) {
    }

    /**
     * 当前推演时间
     *
     * @return
     */
    public long getCurrentTime() {
        return currentTime;
    }

    /**
     * 上次调度时间
     *
     * @return
     */
    protected long getLastTickTime() {
        return lastTickTime;
    }


    protected void tick(TickMsg msg) {
        this.lastTickTime = this.currentTime;
        this.currentTime = msg.logicTime();
        long delta = this.currentTime - this.lastTickTime;
        this.childrenTickCount = 0;
        try {
            try {
                update(delta);
            } catch (Throwable e) {
                log.error(e.getMessage(), e);
            }


            if (!unScheduled.isEmpty()) {
                //尚未调度的实体 默认一秒后调度
                Set<AtomicModel> models = scheduleEvent.computeIfAbsent(currentTime, k -> new HashSet<>(this.children.size()));
                models.addAll(unScheduled);
                unScheduled.clear();
            }
            Map.Entry<Long, Set<AtomicModel>> entry = scheduleEvent.firstEntry();

            boolean completeNow = true;
            if (entry != null && entry.getKey().equals(currentTime)) {
                Set<AtomicModel> willTicked = scheduleEvent.remove(currentTime);
                childrenTickCount = willTicked.size();
                if (childrenTickCount > 0) {
                    completeNow = false;
                    for (AtomicModel model : willTicked) {
                        model.send(msg);
                    }
                }

            }

            if (completeNow) {
                this.onTickComplete();
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 模型更新
     *
     * @param delta 增量时间
     */
    protected void update(long delta) {
    }

    @ThisInvoke
    private void doChildTickComplete(TickCompleteMsg event) {
        Assert.state(event.nextTime() > getCurrentTime(),
                () -> "nextTime(" + event.nextTime() + ") must be greater than currentTime(" + getCurrentTime() + ")  childrenTickCount: " + childrenTickCount);
        childrenTickCount--;
        scheduleEvent.computeIfAbsent(event.nextTime(), k -> new HashSet<>()).add(event.model());
        if (childrenTickCount == 0) {
            this.onTickComplete();
        }
    }

    /**
     * 自身tick完成
     * <p>
     * 内部调用
     */
    @ThisInvoke
    protected void onTickComplete() {
        long next = this.next();
        parent.send(new TickCompleteMsg(this, next));
    }


    /**
     * 下一帧
     *
     * @return
     */
    @ThisInvoke
    protected long next() {
        if (scheduleEvent.isEmpty()) {
            return getCurrentTime() + getFrameLength();
        }
        Map.Entry<Long, Set<AtomicModel>> entry = scheduleEvent.firstEntry();
        Long key = entry.getKey();
        return key;
    }

    protected long getFrameLength() {
        return 1000;
    }

    @Override
    protected boolean onMessage(Object message) {
        boolean f = true;
        switch (message) {
            case TickCompleteMsg event -> doChildTickComplete(event);
            case TickMsg event -> tick(event);
            case AddChildMsg event -> doAddChild(event.child());
            case OnJoinMsg event -> onJoinSimulation(event.logicTime());
            default -> f = false;
        }
        return f;
    }


}