/*
 *   Copyright 2018 The Portico Project
 *
 *   This file is part of portico.
 *
 *   portico is free software; you can redistribute it and/or modify
 *   it under the terms of the Common Developer and Distribution License (CDDL) 
 *   as published by Sun Microsystems. For more information see the LICENSE file.
 *   
 *   Use of this software is strictly AT YOUR OWN RISK!!!
 *   If something bad happens you do not have permission to come crying to me.
 *   (that goes for your lawyer as well)
 *
 */
package csim.toolkit.fom;



import java.util.EnumMap;
import java.util.Map;

/**
 * This class represents the names that a MIM element may assume under different HLA versions.
 * <p/>
 * Beginning with 1516 the specification prefixes all MIM elements (object, attributes, interactions, 
 * parameters and datatypes) with the token HLA and lowercases the proceeding character. For example:
 * 
 * <ul>
 * 	<li>The MIM object class <code>Federation</code> becomes <code>HLAfederation</code></li>
 *  <li>The MIM attribute FederationName becomes <code>HLAfederationName</code></li>
 *  <li>
 *   The MIM interaction class <code>RequestSubscriptions</code> becomes 
 *   <code>HLArequestSubscriptions</code>
 *  </li>
 *  <li>The MIM parameter <code>NumberOfClasses</code> becomes <code>HLAnumberOfClasses</code></li>
 * </ul>
 * 
 * To allow a cross-version lookup, we store a {@link VersionedName} for each MIM type in the MOM, so that
 * we can resolve MOM handles regardless of which naming version is being used.
 * <p/>
 * For the majority of MIM types, the 1516 name is simply the 13 name prefixed with the token "HLA". In 
 * this case a {@link VersionedName} can be constructed from the {@link #from(String)} method, which will
 * determine which version of the name was supplied and generate the names for the other specification 
 * versions.
 * <p/>
 * For a minority of cases, the simple naming pattern does not apply. For example <code>ObjectsOwned</code> 
 * in HLA13 becomes <code>HLAobjectInstancesThatCanBeDeleted</code>. In this case the names for each 
 * version can be explicitly specified in the constructor. For example:
 * 
 * <pre>
 * Map<HLAVersion,String> versionNames = new HashMap<HLAVersion,String>();
 * versionNames.put( HLAVersion.HLA13, "ObjectsOwned" );
 * versionNames.put( HLAVersion.JAVA1, "ObjectsOwned" );
 * versionNames.put( HLAVersion.IEEE1516, "HLAobjectInstancesThatCanBeDeleted" );
 * versionNames.put( HLAVersion.IEEE1516e, "HLAobjectInstancesThatCanBeDeleted" );
 * 
 * VersionedName name = new VersionName( versionNames );
 * </pre>
 * We highly recommend that you check out Tim's thoughts on this subject in the {@link Mom} class as they
 * are quite an entertaining read.
 * 
 * @see HLAVersion
 */
public class VersionedName
{
	//----------------------------------------------------------
	//                    STATIC VARIABLES
	//----------------------------------------------------------

	//----------------------------------------------------------
	//                   INSTANCE VARIABLES
	//----------------------------------------------------------
	private Map<HLAVersion,String> names;

	//----------------------------------------------------------
	//                      CONSTRUCTORS
	//----------------------------------------------------------
	public VersionedName( Map<HLAVersion,String> names )
	{
		this.names = new EnumMap<>( names );
	}

	//----------------------------------------------------------
	//                    INSTANCE METHODS
	//----------------------------------------------------------
	/**
	 * Returns the name stored against a specific version of the specification 
	 *  
	 * @param version the HLA version to return the name for
	 * @return the name as it appears in the version of the specification
	 */
	public String get( HLAVersion version )
	{
		return this.names.get( version );
	}

	////////////////////////////////////////////////////////////////////////////////////////
	///  Accessors and Mutators   //////////////////////////////////////////////////////////
	////////////////////////////////////////////////////////////////////////////////////////


	//----------------------------------------------------------
	//                     STATIC METHODS
	//----------------------------------------------------------
	/**
	 * Creates a {@link VersionedName} from a HLA13 compliant name, using the simple pattern rule.
	 * <p/>
	 * The 1516 names generated by this method will take the form of 
	 * <code>"HLA" + name[0].toLowercase + name.substring(1)</code> unless the name starts with a series
	 * of uppercase characters (e.g. FOMmodule), in which case the lower-case conversion of the first 
	 * character is not performed. For example:
	 * <ul>
	 *  <li>Federation (HLA13) => HLAfederation (IEEE1516)</li>
	 *  <li>FOMModule (HLA13) => HLAFOMModule (IEEE1516)</li>
	 * </ul>
	 * 
	 * @param name13 The HLA13 compliant name to base the versioned name off of
	 * @return a {@link VersionedName} instance representing the names for a MIM element for each
	 *         version of the HLA specification
	 */
	public static VersionedName from13(String name13 )
	{
		Map<HLAVersion,String> nameMap = new EnumMap<>( HLAVersion.class );
		String nameRoot = null;
		if( name13.length() > 1 && 
			Character.isLowerCase(name13.charAt(1)) ) // Don't lower case names like "FOMmodule"
		{
			// Manager -> HLAmanager
			nameRoot = Character.toLowerCase( name13.charAt(0) ) + name13.substring( 1 );
		}
		else
		{
			// FOMModule -> HLAFOMModule
			nameRoot = name13;
		}
		
		String name1516 = "HLA" + nameRoot;
		
		return from( name13, name1516 );
	}
	
	/**
	 * Creates a {@link VersionedName} from a HLA1516 compliant name, using the simple pattern rule.
	 * <p/>
	 * The HLA13 names generated by this method will remove the HLA prefix from the given name, and 
	 * convert the first character of the resulting string to upper case. For example:
	 * <ul>
	 *  <li>HLAfederation (IEEE1516) => Federation (HLA13)</li>
	 * </ul> 
	 * 
	 * @param name1516 The IEEE1516 compliant name to base the versioned name off of
	 * @return a {@link VersionedName} instance representing the names for a MIM element for each
	 *         version of the HLA specification
	 */
	public static VersionedName from1516(String name1516 )
	{
		Map<HLAVersion,String> nameMap = new EnumMap<>( HLAVersion.class );
		String name13 = name1516;
		if( name1516.startsWith("HLA") || name1516.startsWith("hla") )
			name13 = name13.substring( 3 );
		
		if( name13.length() > 1 )
		{
			// HLAmanager -> Manager
			name13 = Character.toUpperCase( name13.charAt(0) ) + name13.substring( 1 );
		}
		
		return from( name13, name1516 );
	}
	
	/**
	 * Generates a {@link VersionedName} instance based on the specified version-specific name.
	 * <p/>
	 * This method automatically determines which version the <code>name</code> conforms to by whether
	 * it starts with the token "HLA" or not. If the token is present, the <code>name</code> is deemed to 
	 * be a IEEE1516 name. If the token is not present, the <code>name</code> is deemed to be a HLA13 
	 * name.
	 * 
	 * @param name a version specific name
	 * @return a {@link VersionedName} instance representing the corresponding names for each version
	 *         of the HLA specification
	 */
	public static VersionedName from(String name )
	{
		if( name.startsWith("HLA") || name.startsWith("hla") )
			return from1516( name );
		else
			return from13( name );
	}
	
	public static VersionedName from(String name13, String name1516 )
	{
		// BS check on the 1516 name
		String name1516Prefix = name1516.substring( 0, 3 ).toLowerCase();
		if( !name1516Prefix.equals("hla") )
			throw new IllegalArgumentException( "1516 name does not start with 'HLA'" );
		
		Map<HLAVersion,String> nameMap = new EnumMap<>( HLAVersion.class );
		nameMap.put( HLAVersion.IEEE1516e, name1516 );
		
		return new VersionedName( nameMap );
	}
}
