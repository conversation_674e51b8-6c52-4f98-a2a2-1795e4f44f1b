/*
 * The IEEE hereby grants a general, royalty-free license to copy, distribute,
 * display and make derivative works from this material, for all purposes,
 * provided that any use of the material contains the following
 * attribution: "Reprinted with permission from IEEE 1516.1(TM)-2010".
 * Should you require additional information, contact the Manager, Standards
 * Intellectual Property, IEEE Standards Association (<EMAIL>).
 */

package csim.toolkit.fom.exception;

/**
 * Public exception class AttributeNotDefined
 */

public final class AttributeNotDefined extends RTIexception {
   public AttributeNotDefined(String msg)
   {
      super(msg);
   }

   public AttributeNotDefined(String message, Throwable cause)
   {
      super(message, cause);
   }
}
