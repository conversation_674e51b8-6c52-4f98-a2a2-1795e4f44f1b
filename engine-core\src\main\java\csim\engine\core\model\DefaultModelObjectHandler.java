package csim.engine.core.model;

import csim.model.api.ModelObject;
import csim.model.api.ModelObjectHandler;
import csim.model.pro.object.BaseEntity;
import csim.model.pro.object.EmbeddedSystem;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class DefaultModelObjectHandler extends ModelObjectHandler {
    @Override
    public void update(long delta) {
        ModelObject modelObject = context.getModelObject();
        String name = null;
        if (modelObject instanceof BaseEntity entity) {
            name = entity.getName();
        }
        if (modelObject instanceof EmbeddedSystem embed) {
            name = embed.getName();
        }

//        log.info("{} {}: 当前 {}", modelObject.getClass().getSimpleName(), name, DateUtil.format(new DateTime(context.getCurrentTime()), "yyyy-MM-dd HH:mm:ss"));
    }

    @Override
    public void onMessage(Object message) {

    }
}
